# Digital Vault Application

Build a secure, offline-first password manager with glassmorphism UI.

## Core Requirements

**Authentication**: Single master password (no registration/accounts)
**Storage**: All secrets in one encrypted `.fetch` file
**Operations**: CRUD operations on secrets (title + content)
**Security**: AES-GCM encryption, master password unlocks everything
**Offline**: Works without internet connection

## Tech Stack

- **Backend**: <PERSON><PERSON> + <PERSON><PERSON> (crypto, file I/O, native app)
- **Frontend**: React + Tailwind (glassmorphism design)
- **Encryption**: AES-GCM or ChaCha20-Poly1305

## Architecture

### Backend Structure
```
src-tauri/src/
├── main.rs          # Tauri app setup
├── commands/        # <PERSON>ri commands
├── services/        # Business logic
├── crypto/          # Encryption/decryption
└── models/          # Data structures
```

### Tauri Commands
- `unlock_vault` - Decrypt file with master password
- `get_secrets` - List all secrets
- `create_secret` - Create new secret
- `update_secret` - Update secret
- `delete_secret` - Delete secret

### Frontend Features
- **Lock screen**: Master password input with glassmorphism styling
- **Dashboard**: Grid/list view of secrets with frosted glass cards
- **Modals**: Create/edit forms with blur backgrounds
- **Design**: Minimal UI with transparency, soft shadows, blur effects

## File Format (`.fetch`)
```json
{
  "version": "1.0",
  "salt": "base64_salt",
  "nonce": "base64_nonce", 
  "encrypted_data": "base64_encrypted_secrets"
}
```

## Security Model
- Master password never stored
- Decryption happens in backend memory only
- File re-encrypted on every change
- No plaintext persistence

## Deliverables
1. **Tauri desktop app** with Rust backend and modular architecture
2. **React frontend** with glassmorphism components
3. **README.md** with setup, security notes, and extension roadmap

## Development Priorities
1. Security first (proper encryption, key handling)
2. Clean code separation (crypto, storage, UI)
3. Extensibility (easy to add tags, folders, file attachments)
4. Minimal dependencies